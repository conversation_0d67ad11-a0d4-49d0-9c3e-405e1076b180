package dto

import (
	"time"

	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
)

type OrderItemRequest struct {
	ProductID  uint64  `json:"product_id" validate:"required"`
	CategoryID uint64  `json:"category_id" validate:"required"`
	Price      float64 `json:"price" validate:"required,gt=0"`
	Quantity   uint64  `json:"quantity" validate:"required,min=1"`
}

type CreateOrderRequest struct {
	Items          []OrderItemRequest `json:"items" validate:"required,min=1"`
	OrderAmount    float64            `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time          `json:"order_timestamp" validate:"required"`
	VoucherCode    *string            `json:"voucher_code,omitempty"`
}

type OrderItemResponse struct {
	ProductID  uint64  `json:"product_id"`
	CategoryID uint64  `json:"category_id"`
	Price      float64 `json:"price"`
	Quantity   uint64  `json:"quantity"`
}

type OrderResponse struct {
	ID                 uint64              `json:"id"`
	UserID             uint64              `json:"user_id"`
	Items              []OrderItemResponse `json:"items"`
	OrderAmount        float64             `json:"order_amount"`
	CalculationStatus  string              `json:"calculation_status"`
	CalculationMessage string              `json:"calculation_message"`
	CreatedAt          time.Time           `json:"created_at"`
	UpdatedAt          time.Time           `json:"updated_at"`
	AppliedVoucherID   *uint64             `json:"applied_voucher_id,omitempty"`
	AppliedVoucherCode *string             `json:"applied_voucher_code,omitempty"`
	DiscountAmount     *float64            `json:"discount_amount,omitempty"`
}

type ListOrdersRequest struct {
	Page   int32   `json:"page" query:"page"`
	Limit  int32   `json:"limit" query:"limit"`
	Search *string `json:"search" query:"search"`
}

type ListOrdersByVoucherRequest struct {
	VoucherID uint64 `json:"voucher_id" query:"voucher_id"`
	Page      int32  `json:"page" query:"page"`
	Limit     int32  `json:"limit" query:"limit"`
}

type ListOrdersResponse struct {
	Data  []OrderResponse `json:"data"`
	Total int32           `json:"total"`
	Page  int32           `json:"page"`
	Limit int32           `json:"limit"`
}

type UserOrderCountResponse struct {
	OrderCount uint64 `json:"order_count"`
}

type UserVoucherUsageCountResponse struct {
	UsageCount uint64 `json:"usage_count"`
}

func ToOrderResponse(order *proto_order_v1.Order) *OrderResponse {
	if order == nil {
		return nil
	}

	var items []OrderItemResponse
	for _, item := range order.Items {
		items = append(items, OrderItemResponse{
			ProductID: item.ProductId,
			Quantity:  item.Quantity,
		})
	}

	response := &OrderResponse{
		ID:                 order.Id,
		UserID:             order.UserId,
		Items:              items,
		OrderAmount:        order.OrderAmount,
		CalculationStatus:  order.CalculationStatus,
		CalculationMessage: order.CalculationMessage,
		CreatedAt:          order.CreatedAt.AsTime(),
	}

	if order.AppliedVoucherId != nil {
		response.AppliedVoucherID = order.AppliedVoucherId
	}

	return response
}

func ToOrderItemProto(item OrderItemRequest) *proto_order_v1.OrderItem {
	return &proto_order_v1.OrderItem{
		ProductId:  item.ProductID,
		CategoryId: item.CategoryID,
		Price:      item.Price,
		Quantity:   item.Quantity,
	}
}
