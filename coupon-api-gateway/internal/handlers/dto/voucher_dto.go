package dto

import (
	"time"

	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type CreateVoucherRequest struct {
	VoucherCode       string    `json:"voucher_code" validate:"required,min=3,max=50"`
	Title             string    `json:"title" validate:"required,min=1,max=255"`
	Description       string    `json:"description"`
	DiscountTypeID    uint64    `json:"discount_type_id" validate:"required"`
	DiscountValue     float64   `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       string    `json:"usage_method" validate:"required,oneof=MANUAL AUTO"`
	ValidFrom         time.Time `json:"valid_from" validate:"required"`
	ValidUntil        time.Time `json:"valid_until" validate:"required"`
	MaxUsageCount     *int32    `json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32    `json:"max_usage_per_user,omitempty"`
	MinOrderAmount    float64   `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64  `json:"max_discount_amount,omitempty"`
}

type UpdateVoucherRequest struct {
	Title             string    `json:"title" validate:"required,min=1,max=255"`
	Description       string    `json:"description"`
	DiscountTypeID    uint64    `json:"discount_type_id" validate:"required"`
	DiscountValue     float64   `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       string    `json:"usage_method" validate:"required,oneof=MANUAL AUTO"`
	Status            string    `json:"status" validate:"required,oneof=ACTIVE INACTIVE EXPIRED"`
	MinOrderAmount    float64   `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64  `json:"max_discount_amount,omitempty"`
	MaxUsageCount     *int32    `json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32    `json:"max_usage_per_user,omitempty"`
	ValidFrom         time.Time `json:"valid_from" validate:"required"`
	ValidUntil        time.Time `json:"valid_until" validate:"required"`

	ProductRestrictions []*VoucherProductRestriction `json:"product_restrictions,omitempty"`
	TimeRestrictions    []*VoucherTimeRestriction    `json:"time_restrictions,omitempty"`
	UserEligibility     []*VoucherUserEligibility    `json:"user_eligibility,omitempty"`
}

type CheckVoucherEligibilityRequest struct {
	VoucherCode    string     `json:"voucher_code" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp *time.Time `json:"order_timestamp,omitempty"`
	CartItems      []CartItem `json:"cart_items,omitempty"`
}

type ListAutoEligibleVouchersRequest struct {
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp *time.Time `json:"order_timestamp,omitempty"`
	CartItems      []CartItem `json:"cart_items,omitempty"`
}

type ListVouchersRequest struct {
	Page           int     `json:"page" query:"page"`
	Limit          int     `json:"limit" query:"limit"`
	Search         string  `json:"search" query:"search"`
	DiscountTypeID *uint64 `json:"discount_type_id" query:"discount_type_id"`
	UsageMethod    string  `json:"usage_method" query:"usage_method"`
	Status         string  `json:"status" query:"status"`
	SortBy         string  `json:"sort_by" query:"sort_by"`
	SortOrder      string  `json:"sort_order" query:"sort_order"`
}

type CartItem struct {
	ProductID  *uint64 `json:"product_id,omitempty"`
	CategoryID *uint64 `json:"category_id,omitempty"`
	Quantity   uint64  `json:"quantity" validate:"required,gt=0"`
	Price      float64 `json:"price" validate:"required,gte=0"`
}

type ListVoucherItemResponse struct {
	ID               uint64  `json:"id"`
	VoucherCode      string  `json:"voucher_code"`
	Title            string  `json:"title"`
	DiscountTypeName string  `json:"discount_type_name"`
	DiscountTypeCode string  `json:"discount_type_code"`
	DiscountValue    float64 `json:"discount_value"`
	UsageMethod      string  `json:"usage_method" validate:"oneof=MANUAL AUTO"`
	ValidFrom        string  `json:"valid_from"`
	ValidUntil       string  `json:"valid_until"`
	CreatedBy        uint64  `json:"created_by"`
	Status           string  `json:"status"`
	CreatedAt        string  `json:"created_at"`
	UpdatedAt        string  `json:"updated_at"`
}

type VoucherResponse struct {
	ID                  uint64                       `json:"id"`
	VoucherCode         string                       `json:"voucher_code"`
	Title               string                       `json:"title"`
	Description         string                       `json:"description"`
	DiscountType        *DiscountTypeResponse        `json:"discount_type,omitempty"`
	DiscountValue       float64                      `json:"discount_value"`
	UsageMethod         string                       `json:"usage_method" validate:"oneof=MANUAL AUTO"`
	ValidFrom           string                       `json:"valid_from"`
	ValidUntil          string                       `json:"valid_until"`
	MaxUsageCount       *int32                       `json:"max_usage_count,omitempty"`
	MaxUsagePerUser     *int32                       `json:"max_usage_per_user,omitempty"`
	CurrentUsageCount   int32                        `json:"current_usage_count"`
	MinOrderAmount      float64                      `json:"min_order_amount"`
	MaxDiscountAmount   *float64                     `json:"max_discount_amount,omitempty"`
	CreatedBy           uint64                       `json:"created_by"`
	Status              string                       `json:"status"`
	CreatedAt           string                       `json:"created_at"`
	UpdatedAt           string                       `json:"updated_at"`
	ProductRestrictions []*VoucherProductRestriction `json:"product_restrictions,omitempty"`
	TimeRestrictions    []*VoucherTimeRestriction    `json:"time_restrictions,omitempty"`
	UserEligibility     []*VoucherUserEligibility    `json:"user_eligibility,omitempty"`
	UserEligibilityType string                       `json:"user_eligibility_type,omitempty"`
	UserUsage           []*UserVoucherUsage          `json:"user_usage,omitempty"`
	TotalSavings        float64                      `json:"total_savings"`
	UniqueUsers         int32                        `json:"unique_users"`
}

type DiscountTypeResponse struct {
	ID          uint64    `json:"id"`
	TypeCode    string    `json:"type_code"`
	TypeName    string    `json:"type_name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type VoucherProductRestriction struct {
	ID           uint64  `json:"id"`
	VoucherID    uint64  `json:"voucher_id"`
	ProductID    *uint64 `json:"product_id,omitempty"`
	CategoryID   *uint64 `json:"category_id,omitempty"`
	IsIncluded   bool    `json:"is_included"`
	CreatedAt    string  `json:"created_at"`
	ProductName  *string `json:"product_name,omitempty"`
	CategoryName *string `json:"category_name,omitempty"`
}

type VoucherTimeRestriction struct {
	ID                   uint64   `json:"id"`
	VoucherID            uint64   `json:"voucher_id"`
	RestrictionType      string   `json:"restriction_type"`
	AllowedDaysOfWeek    []int32  `json:"allowed_days_of_week,omitempty"`
	AllowedHoursStart    *int32   `json:"allowed_hours_start,omitempty"`
	AllowedHoursEnd      *int32   `json:"allowed_hours_end,omitempty"`
	SpecificDates        []string `json:"specific_dates,omitempty"`
	RecurrencePattern    *string  `json:"recurrence_pattern,omitempty"`
	RecurrenceDayOfMonth *int32   `json:"recurrence_day_of_month,omitempty"`
	RecurrenceMonth      *int32   `json:"recurrence_month,omitempty"`
	RecurrenceDayOfWeek  *int32   `json:"recurrence_day_of_week,omitempty"`
	CreatedAt            string   `json:"created_at"`
}

type VoucherUserEligibility struct {
	ID                uint64  `json:"id"`
	VoucherID         uint64  `json:"voucher_id"`
	UserID            *uint64 `json:"user_id,omitempty"`
	UserType          string  `json:"user_type,omitempty"`
	MinAccountAgeDays *int32  `json:"min_account_age_days,omitempty"`
	MaxAccountAgeDays *int32  `json:"max_account_age_days,omitempty"`
	MinPreviousOrders *uint64 `json:"min_previous_orders,omitempty"`
	MaxPreviousOrders *uint64 `json:"max_previous_orders,omitempty"`
	CreatedAt         string  `json:"created_at"`
}

type VoucherOrderUsage struct {
	OrderID     uint64  `json:"order_id"`
	UsedAt      string  `json:"used_at"`
	OrderAmount float64 `json:"order_amount"`
	Status      string  `json:"status"`
}

type UserVoucherUsage struct {
	UserID     uint64               `json:"user_id"`
	UsageCount int32                `json:"usage_count"`
	FullName   string               `json:"full_name"`
	Email      string               `json:"email"`
	Type       string               `json:"type"`
	Orders     []*VoucherOrderUsage `json:"orders,omitempty"`
}

type VoucherEligibilityResponse struct {
	Eligible         bool    `json:"eligible"`
	Message          string  `json:"message"`
	VoucherID        uint64  `json:"voucher_id"`
	DiscountAmount   float64 `json:"discount_amount"`
	DiscountTypeName string  `json:"discount_type_name"`
	DiscountTypeCode string  `json:"discount_type_code"`
	VoucherCode      string  `json:"voucher_code"`
	Title            string  `json:"title"`
	Description      string  `json:"description"`
	Status           *string `json:"status,omitempty"`
}

type ListVouchersResponse struct {
	Data  []*ListVoucherItemResponse `json:"data"`
	Total int32                      `json:"total"`
	Page  int32                      `json:"page"`
	Limit int32                      `json:"limit"`
}

func ToListVoucherItemResponse(voucher *proto_voucher_v1.Voucher) *ListVoucherItemResponse {
	if voucher == nil {
		return nil
	}

	response := &ListVoucherItemResponse{
		ID:            voucher.Id,
		VoucherCode:   voucher.VoucherCode,
		Title:         voucher.Title,
		DiscountValue: voucher.DiscountValue,
		UsageMethod:   convertUsageMethodToString(voucher.UsageMethod),
		ValidFrom:     voucher.ValidFrom.AsTime().Format(time.RFC3339),
		ValidUntil:    voucher.ValidUntil.AsTime().Format(time.RFC3339),
		CreatedBy:     voucher.CreatedBy,
		Status:        convertVoucherStatusToString(voucher.Status),
		CreatedAt:     voucher.CreatedAt.AsTime().Format(time.RFC3339),
		UpdatedAt:     voucher.UpdatedAt.AsTime().Format(time.RFC3339),
	}

	if voucher.DiscountType != nil {
		response.DiscountTypeName = voucher.DiscountType.TypeName
		response.DiscountTypeCode = voucher.DiscountType.TypeCode
	}

	return response
}

func ToVoucherResponse(voucher *proto_voucher_v1.Voucher) *VoucherResponse {
	if voucher == nil {
		return nil
	}

	response := &VoucherResponse{
		ID:                voucher.Id,
		VoucherCode:       voucher.VoucherCode,
		Title:             voucher.Title,
		Description:       voucher.Description,
		DiscountValue:     voucher.DiscountValue,
		UsageMethod:       convertUsageMethodToString(voucher.UsageMethod),
		ValidFrom:         voucher.ValidFrom.AsTime().Format(time.RFC3339),
		ValidUntil:        voucher.ValidUntil.AsTime().Format(time.RFC3339),
		CurrentUsageCount: voucher.CurrentUsageCount,
		MinOrderAmount:    voucher.MinOrderAmount,
		CreatedBy:         voucher.CreatedBy,
		Status:            convertVoucherStatusToString(voucher.Status),
		CreatedAt:         voucher.CreatedAt.AsTime().Format(time.RFC3339),
		UpdatedAt:         voucher.UpdatedAt.AsTime().Format(time.RFC3339),
		TotalSavings:      voucher.TotalSavings,
		UniqueUsers:       voucher.UniqueUsers,
	}

	if voucher.MaxUsageCount != nil {
		response.MaxUsageCount = voucher.MaxUsageCount
	}

	if voucher.MaxUsagePerUser != nil {
		response.MaxUsagePerUser = voucher.MaxUsagePerUser
	}

	if voucher.MaxDiscountAmount != nil {
		response.MaxDiscountAmount = voucher.MaxDiscountAmount
	}

	if voucher.DiscountType != nil {
		response.DiscountType = ToDiscountTypeResponse(voucher.DiscountType)
	}

	if len(voucher.ProductRestrictions) > 0 {
		for _, pr := range voucher.ProductRestrictions {
			response.ProductRestrictions = append(response.ProductRestrictions, ToVoucherProductRestriction(pr))
		}
	}

	if len(voucher.TimeRestrictions) > 0 {
		for _, tr := range voucher.TimeRestrictions {
			response.TimeRestrictions = append(response.TimeRestrictions, ToVoucherTimeRestriction(tr))
		}
	}

	if len(voucher.UserEligibilityRules) > 0 {
		for _, ue := range voucher.UserEligibilityRules {
			response.UserEligibility = append(response.UserEligibility, ToVoucherUserEligibility(ue))
		}
	}

	if len(voucher.UserUsages) > 0 {
		for _, uu := range voucher.UserUsages {
			response.UserUsage = append(response.UserUsage, ToUserVoucherUsage(uu))
		}
	}

	response.UserEligibilityType = voucher.UserEligibilityType

	return response
}

func ToDiscountTypeResponse(discountType *proto_voucher_v1.DiscountType) *DiscountTypeResponse {
	if discountType == nil {
		return nil
	}

	return &DiscountTypeResponse{
		ID:          discountType.Id,
		TypeCode:    discountType.TypeCode,
		TypeName:    discountType.TypeName,
		Description: discountType.Description,
		IsActive:    discountType.IsActive,
		CreatedAt:   discountType.CreatedAt.AsTime(),
		UpdatedAt:   discountType.UpdatedAt.AsTime(),
	}
}

func ToVoucherProductRestriction(pr *proto_voucher_v1.VoucherProductRestriction) *VoucherProductRestriction {
	if pr == nil {
		return nil
	}

	restriction := &VoucherProductRestriction{
		ID:         pr.Id,
		VoucherID:  pr.VoucherId,
		IsIncluded: pr.IsIncluded,
		CreatedAt:  pr.CreatedAt.AsTime().Format(time.RFC3339),
	}

	if pr.ProductId != nil {
		restriction.ProductID = pr.ProductId
	}

	if pr.CategoryId != nil {
		restriction.CategoryID = pr.CategoryId
	}

	if pr.ProductName != nil {
		restriction.ProductName = pr.ProductName
	}

	if pr.CategoryName != nil {
		restriction.CategoryName = pr.CategoryName
	}

	return restriction
}

func ToVoucherTimeRestriction(tr *proto_voucher_v1.VoucherTimeRestriction) *VoucherTimeRestriction {
	if tr == nil {
		return nil
	}

	restriction := &VoucherTimeRestriction{
		ID:              tr.Id,
		VoucherID:       tr.VoucherId,
		RestrictionType: tr.RestrictionType.String(),
		CreatedAt:       tr.CreatedAt.AsTime().Format(time.RFC3339),
	}

	if len(tr.AllowedDaysOfWeeks) > 0 {
		restriction.AllowedDaysOfWeek = tr.AllowedDaysOfWeeks
	}

	if tr.AllowedHoursStart != nil {
		restriction.AllowedHoursStart = tr.AllowedHoursStart
	}

	if tr.AllowedHoursEnd != nil {
		restriction.AllowedHoursEnd = tr.AllowedHoursEnd
	}

	if len(tr.SpecificDates) > 0 {
		for _, date := range tr.SpecificDates {
			restriction.SpecificDates = append(restriction.SpecificDates, date.AsTime().Format("2006-01-02"))
		}
	}

	if tr.RecurrencePattern != nil {
		pattern := tr.RecurrencePattern.String()
		restriction.RecurrencePattern = &pattern
	}

	if tr.RecurrenceDayOfMonth != nil {
		restriction.RecurrenceDayOfMonth = tr.RecurrenceDayOfMonth
	}

	if tr.RecurrenceMonth != nil {
		restriction.RecurrenceMonth = tr.RecurrenceMonth
	}

	if tr.RecurrenceDayOfWeek != nil {
		restriction.RecurrenceDayOfWeek = tr.RecurrenceDayOfWeek
	}

	return restriction
}

func ToVoucherUserEligibility(ue *proto_voucher_v1.VoucherUserEligibility) *VoucherUserEligibility {
	if ue == nil {
		return nil
	}

	eligibility := &VoucherUserEligibility{
		ID:        ue.Id,
		VoucherID: ue.VoucherId,
		CreatedAt: ue.CreatedAt.AsTime().Format(time.RFC3339),
	}

	if ue.UserId != nil {
		eligibility.UserID = ue.UserId
	}

	if ue.UserType != nil {
		eligibility.UserType = *ue.UserType
	}

	if ue.MinAccountAgeDays != nil {
		eligibility.MinAccountAgeDays = ue.MinAccountAgeDays
	}

	if ue.MaxAccountAgeDays != nil {
		eligibility.MaxAccountAgeDays = ue.MaxAccountAgeDays
	}

	if ue.MinPreviousOrders != nil {
		eligibility.MinPreviousOrders = ue.MinPreviousOrders
	}

	if ue.MaxPreviousOrders != nil {
		eligibility.MaxPreviousOrders = ue.MaxPreviousOrders
	}

	return eligibility
}

func ToUserVoucherUsage(uu *proto_voucher_v1.UserVoucherUsage) *UserVoucherUsage {
	if uu == nil {
		return nil
	}

	usage := &UserVoucherUsage{
		UserID:     uu.UserId,
		UsageCount: uu.UsageCount,
		FullName:   uu.FullName,
		Email:      uu.Email,
		Type:       uu.Type,
	}

	if len(uu.Orders) > 0 {
		for _, order := range uu.Orders {
			usage.Orders = append(usage.Orders, ToVoucherOrderUsage(order))
		}
	}

	return usage
}

func ToVoucherOrderUsage(ou *proto_voucher_v1.VoucherOrderUsage) *VoucherOrderUsage {
	if ou == nil {
		return nil
	}

	return &VoucherOrderUsage{
		OrderID:     ou.OrderId,
		UsedAt:      ou.UsedAt.AsTime().Format(time.RFC3339),
		OrderAmount: ou.OrderAmount,
		Status:      ou.Status,
	}
}

func convertUsageMethodToString(method proto_voucher_v1.UsageMethod) string {
	switch method {
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL:
		return "MANUAL"
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO:
		return "AUTO"
	default:
		return "MANUAL"
	}
}

func convertVoucherStatusToString(status proto_voucher_v1.VoucherStatus) string {
	switch status {
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE:
		return "ACTIVE"
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE:
		return "INACTIVE"
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED:
		return "EXPIRED"
	default:
		return "ACTIVE"
	}
}

func convertStringToUsageMethod(method string) proto_voucher_v1.UsageMethod {
	switch method {
	case "MANUAL":
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	case "AUTO":
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO
	default:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	}
}

func convertStringToVoucherStatus(status string) proto_voucher_v1.VoucherStatus {
	switch status {
	case "ACTIVE":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	case "INACTIVE":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE
	case "EXPIRED":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED
	default:
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	}
}

func ConvertStringToUsageMethod(method string) proto_voucher_v1.UsageMethod {
	return convertStringToUsageMethod(method)
}

func ConvertStringToVoucherStatus(status string) proto_voucher_v1.VoucherStatus {
	return convertStringToVoucherStatus(status)
}
