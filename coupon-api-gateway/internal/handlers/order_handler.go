package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type OrderHandler struct {
	orderClient *clients.OrderClient
	logger      *logging.Logger
}

type orderIDParam struct {
	ID int `param:"id" validate:"required"`
}

func NewOrderHandler(orderClient *clients.OrderClient, logger *logging.Logger) *OrderHandler {
	return &OrderHandler{
		orderClient: orderClient,
		logger:      logger,
	}
}

func (h *OrderHandler) RegisterProtectedRoutes(g *echo.Group, spec *docs.OpenApi) {
	createOrderRoute := g.POST("/orders", h.HandleCreateOrder)
	echoAdapter.AddRoute[dto.CreateOrderRequest, dto.OrderResponse](spec, createOrderRoute, docs.OperationObject{Tags: []string{"Orders API"}})

	getOrderRoute := g.GET("/orders/:id", h.HandleGetOrder)
	echoAdapter.AddRoute[orderIDParam, dto.OrderResponse](spec, getOrderRoute, docs.OperationObject{Tags: []string{"Orders API"}})

	listOrdersRoute := g.GET("/orders", h.HandleListOrders)
	echoAdapter.AddRoute[dto.ListOrdersRequest, dto.ListOrdersResponse](spec, listOrdersRoute, docs.OperationObject{Tags: []string{"Orders API"}})

	getUserOrderCountRoute := g.GET("/users/me/orders/count", h.HandleGetUserOrderCount)
	echoAdapter.AddRoute[struct{}, dto.UserOrderCountResponse](spec, getUserOrderCountRoute, docs.OperationObject{Tags: []string{"Orders API"}})

	getUserVoucherUsageCountRoute := g.GET("/users/me/vouchers/:voucher_id/usage-count", h.HandleGetUserVoucherUsageCount)
	echoAdapter.AddRoute[struct{}, dto.UserVoucherUsageCountResponse](spec, getUserVoucherUsageCountRoute, docs.OperationObject{Tags: []string{"Orders API"}})

	listOrdersByVoucherRoute := g.GET("/vouchers/:voucher_id/orders", h.HandleListOrdersByVoucher)
	echoAdapter.AddRoute[dto.ListOrdersByVoucherRequest, dto.ListOrdersResponse](spec, listOrdersByVoucherRoute, docs.OperationObject{Tags: []string{"Orders API"}})
}

func (h *OrderHandler) HandleCreateOrder(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	var req dto.CreateOrderRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}

	if err := c.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	var protoItems []*proto_order_v1.OrderItem
	for _, item := range req.Items {
		protoItems = append(protoItems, dto.ToOrderItemProto(item))
	}

	grpcReq := &proto_order_v1.CreateOrderRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		UserId:         userID,
		Items:          protoItems,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: timestamppb.New(req.OrderTimestamp),
	}

	if req.VoucherCode != nil {
		grpcReq.VoucherCode = req.VoucherCode
	}

	res, err := h.orderClient.CreateOrder(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	orderResponse := dto.ToOrderResponse(res.Order)
	return c.JSON(http.StatusCreated, orderResponse)
}

func (h *OrderHandler) HandleGetOrder(c echo.Context) error {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid order ID"))
	}

	grpcReq := &proto_order_v1.GetOrderRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		OrderId: orderID,
	}

	res, err := h.orderClient.GetOrder(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	orderResponse := dto.ToOrderResponse(res.Order)
	return c.JSON(http.StatusOK, orderResponse)
}

func (h *OrderHandler) HandleListOrders(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	page := int32(1)
	if pageStr := c.QueryParam("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = int32(p)
		}
	}

	limit := int32(10)
	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = int32(l)
		}
	}

	search := c.QueryParam("search")

	grpcReq := &proto_order_v1.ListOrdersRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		UserId: &userID,
		Pagination: &proto_common_v1.PaginationRequest{
			Page:     page,
			PageSize: limit,
		},
	}

	if search != "" {
		grpcReq.Search = &search
	}

	res, err := h.orderClient.ListOrders(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	var orders []dto.OrderResponse
	for _, order := range res.Orders {
		orders = append(orders, *dto.ToOrderResponse(order))
	}

	response := dto.ListOrdersResponse{
		Data:  orders,
		Total: res.Total,
		Page:  res.Page,
		Limit: res.Limit,
	}

	return c.JSON(http.StatusOK, response)
}

func (h *OrderHandler) HandleGetUserOrderCount(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	grpcReq := &proto_order_v1.GetUserOrderCountRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		UserId: userID,
	}

	res, err := h.orderClient.GetUserOrderCount(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	response := dto.UserOrderCountResponse{
		OrderCount: res.OrderCount,
	}

	return c.JSON(http.StatusOK, response)
}

func (h *OrderHandler) HandleGetUserVoucherUsageCount(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	voucherIDStr := c.Param("voucher_id")
	voucherID, err := strconv.ParseUint(voucherIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid voucher ID"))
	}

	grpcReq := &proto_order_v1.GetUserVoucherUsageCountRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		UserId:    userID,
		VoucherId: voucherID,
	}

	res, err := h.orderClient.GetUserVoucherUsageCount(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	response := dto.UserVoucherUsageCountResponse{
		UsageCount: res.UsageCount,
	}

	return c.JSON(http.StatusOK, response)
}

func (h *OrderHandler) HandleListOrdersByVoucher(c echo.Context) error {
	voucherIDStr := c.Param("voucher_id")
	voucherID, err := strconv.ParseUint(voucherIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid voucher ID"))
	}

	page := int32(1)
	if pageStr := c.QueryParam("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = int32(p)
		}
	}

	limit := int32(10)
	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = int32(l)
		}
	}

	grpcReq := &proto_order_v1.ListOrdersByVoucherRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		VoucherId: voucherID,
		Pagination: &proto_common_v1.PaginationRequest{
			Page:     page,
			PageSize: limit,
		},
	}

	res, err := h.orderClient.ListOrdersByVoucher(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	var orders []dto.OrderResponse
	for _, order := range res.Orders {
		orders = append(orders, *dto.ToOrderResponse(order))
	}

	response := dto.ListOrdersResponse{
		Data:  orders,
		Total: res.Total,
		Page:  res.Page,
		Limit: res.Limit,
	}

	return c.JSON(http.StatusOK, response)
}
