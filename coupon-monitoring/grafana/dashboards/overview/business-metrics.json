{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Key business metrics across all services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "User Registrations/hour"}, "properties": [{"id": "unit", "value": "short"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "blue"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "User Logins/hour"}, "properties": [{"id": "unit", "value": "short"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "light-blue"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Voucher Redemptions/hour"}, "properties": [{"id": "unit", "value": "short"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "green"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Product Queries/hour"}, "properties": [{"id": "unit", "value": "short"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "orange"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Auth Success Rate"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "purple"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"user-service\", operation=\"user_registration\", status=\"success\"}[1h]))", "interval": "", "legendFormat": "User Registrations/hour", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"user-service\", operation=\"user_login\", status=\"success\"}[1h]))", "interval": "", "legendFormat": "User Logins/hour", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"voucher-service\", operation=\"voucher_redemption\", status=\"success\"}[1h]))", "interval": "", "legendFormat": "Voucher Redemptions/hour", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"product-service\", operation=\"product_query\"}[1h]))", "interval": "", "legendFormat": "Product Queries/hour", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"order-service\", operation=\"order_creation\", status=\"success\"}[1h]))", "interval": "", "legendFormat": "Orders Created/hour", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(increase(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\", status=\"success\"}[1h]))", "interval": "", "legendFormat": "Notifications Sent/hour", "refId": "F"}], "title": "Business KPIs", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "User registration trends over time", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Registrations", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"user-service\", operation=\"user_registration\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Successful Registrations/min", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"user-service\", operation=\"user_registration\", status=\"error\"}[5m])) * 60", "interval": "", "legendFormat": "Failed Registrations/min", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"user-service\", operation=\"user_login\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Successful Logins/min", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"user-service\", operation=\"user_login\", status=\"error\"}[5m])) * 60", "interval": "", "legendFormat": "Failed Logins/min", "refId": "D"}], "title": "User Registration Rate & Login Rates", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Voucher operations and redemption patterns", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Operations/min", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"voucher-service\", operation=\"voucher_redemption\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Successful Redemptions/min", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"voucher-service\", operation=\"voucher_creation\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Voucher Creations/min", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"voucher-service\", operation=\"voucher_validation\"}[5m])) * 60", "interval": "", "legendFormat": "Voucher Validations/min", "refId": "C"}], "title": "Voucher Operations", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Order processing and completion rates", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Orders/min", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"order-service\", operation=\"order_creation\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Successful Orders/min", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"order-service\", operation=\"order_processing\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Orders with Vouchers/min", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"order-service\", operation=\"order_creation\", status=\"error\"}[5m])) * 60", "interval": "", "legendFormat": "Failed Orders/min", "refId": "C"}], "title": "Order Processing Metrics", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Notification delivery and engagement metrics", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Notifications/min", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Notifications Sent/min", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"notification-service\", operation=~\"notification_.*\", status=\"failed\"}[5m])) * 60", "interval": "", "legendFormat": "Failed Notifications/min", "refId": "B"}], "title": "Notification Metrics", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Product search and discovery patterns", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"product_query\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Successful Queries/min", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"product_query\", status=\"error\"}[5m])) * 60", "interval": "", "legendFormat": "Failed Queries/min", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"category_list\", status=\"success\"}[5m])) * 60", "interval": "", "legendFormat": "Category Browsing/min", "refId": "C"}], "title": "Product Query Metrics", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["production", "business", "kpi"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Business Metrics Dashboard", "uid": "business-metrics", "version": 1, "weekStart": ""}