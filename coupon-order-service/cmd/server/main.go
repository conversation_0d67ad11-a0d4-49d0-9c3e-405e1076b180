package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-order-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/service"
	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("config error: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, "json")
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("tracer error: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)

	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("db error: %v", err)
	}

	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []any{
		&model.Order{},
		&model.OrderItem{},
	}

	if err := autoMigrator.AutoMigrate(models...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}

	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)

	authClient, err := clients.NewAuthClient(cfg.DownstreamServices.AuthServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.Name, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("auth client error: %v", err)
	}

	voucherClient, err := clients.NewVoucherClient(cfg.DownstreamServices.VoucherServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.Name, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("voucher client error: %v", err)
	}

	repo := repository.NewOrderRepository(db, redisClient, logger)
	eventPublisher := service.NewOrderEventPublisher(cfg, logger)
	businessMetrics := metrics.NewBusinessMetrics(appMetrics, cfg.Service.Name)
	svc := service.NewOrderService(repo, voucherClient, eventPublisher, logger, businessMetrics)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		authFunc := middleware.CreateServiceAuthFunc(authClient)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, healthChecker, appMetrics, logger)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	authClient.Close()
	voucherClient.Close()
	eventPublisher.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.OrderService, authFunc grpc_auth.AuthFunc) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc, cfg.Service.Name)
	grpcHandler := grpc_handler.NewOrderServer(svc)
	proto_order_v1.RegisterOrderServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

func startHTTPServer(ctx context.Context, cfg *config.Config, healthChecker *health.HealthChecker, metrics *metrics.Metrics, logger *logging.Logger) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET("/metrics", echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
