package clients

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type VoucherClient struct {
	client proto_voucher_v1.VoucherServiceClient
	conn   *shared_grpc.Client
	logger *logging.Logger
}

func NewVoucherClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, serviceName, clientID, clientKey string) (*VoucherClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, serviceName, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for voucher service: %w", err)
	}

	return &VoucherClient{
		client: proto_voucher_v1.NewVoucherServiceClient(client.Conn),
		conn:   client,
		logger: logger,
	}, nil
}

func (c *VoucherClient) Close() {
	c.conn.Close()
}

func (c *VoucherClient) CheckVoucherEligibility(ctx context.Context, userID uint64, voucherCode string, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) (*model.VoucherEligibilityResponse, error) {
	log := c.logger.WithContext(ctx)

	var cartItems []*proto_voucher_v1.CartItem
	for _, item := range items {
		cartItems = append(cartItems, &proto_voucher_v1.CartItem{
			ProductId:  &item.ProductID,
			CategoryId: &item.CategoryID,
			Price:      item.Price,
			Quantity:   item.Quantity,
		})
	}

	req := &proto_voucher_v1.CheckVoucherEligibilityRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("order-service-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "order-service",
		},
		VoucherCode:    voucherCode,
		UserId:         userID,
		OrderAmount:    orderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		CartItems:      cartItems,
	}

	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.CheckVoucherEligibility(ctx, req)
	if err != nil {
		log.Errorf("Failed to check voucher eligibility: %v", err)
		return nil, err
	}

	if resp.Error != nil {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  resp.Error.Message,
		}, nil
	}

	result := &model.VoucherEligibilityResponse{
		Eligible:         resp.Eligible,
		VoucherID:        resp.VoucherId,
		VoucherCode:      resp.VoucherCode,
		DiscountAmount:   resp.DiscountAmount,
		DiscountTypeName: resp.DiscountTypeName,
		DiscountTypeCode: resp.DiscountTypeCode,
		Message:          resp.Message,
		Title:            resp.Title,
		Description:      resp.Description,
	}

	if resp.Status != 0 {
		statusStr := resp.Status.String()
		result.Status = &statusStr
	}

	return result, nil
}

func (c *VoucherClient) IncrementVoucherUsage(ctx context.Context, voucherID, userID, orderID uint64, orderAmount, discountAmount float64, orderTimestamp time.Time) error {
	log := c.logger.WithContext(ctx)

	req := &proto_voucher_v1.IncrementVoucherUsageRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("order-service-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "order-service",
		},
		VoucherId:      voucherID,
		UserId:         userID,
		OrderAmount:    orderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		OrderId:        orderID,
		DiscountAmount: discountAmount,
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.IncrementVoucherUsage(ctx, req)
	if err != nil {
		log.Errorf("Failed to increment voucher usage: %v", err)
		return err
	}

	if resp.Error != nil {
		log.Errorf("Error incrementing voucher usage: %s", resp.Error.Message)
		return fmt.Errorf("%s", resp.Error.Message)
	}

	if !resp.Success {
		log.Errorf("Failed to increment voucher usage: %s", resp.Message)
		return fmt.Errorf("failed to increment voucher usage: %s", resp.Message)
	}

	log.Infof("Successfully incremented usage for voucher %d to %d", voucherID, resp.NewUsageCount)
	return nil
}
