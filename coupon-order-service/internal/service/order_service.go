package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/repository"
	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type OrderService interface {
	CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error)
	GetOrder(ctx context.Context, orderID uint64) (*model.Order, error)
	ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error)
	ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error)
	GetUserOrderCount(ctx context.Context, userID uint64) (int64, error)
	GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int64, error)
}

type orderService struct {
	repo            repository.OrderRepository
	voucherClient   *clients.VoucherClient
	eventPublisher  *OrderEventPublisher
	logger          *logging.Logger
	businessMetrics *metrics.BusinessMetrics
}

func NewOrderService(repo repository.OrderRepository, voucherClient *clients.VoucherClient, eventPublisher *OrderEventPublisher, logger *logging.Logger, businessMetrics *metrics.BusinessMetrics) OrderService {
	return &orderService{
		repo:            repo,
		voucherClient:   voucherClient,
		eventPublisher:  eventPublisher,
		logger:          logger,
		businessMetrics: businessMetrics,
	}
}

func (s *orderService) CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error) {
	start := time.Now()

	log := s.logger.WithContext(ctx)
	log.Infof("Creating order for user %d with amount %.2f", req.UserID, req.OrderAmount)

	var selectedVoucher *model.VoucherEligibilityResponse
	calculationStatus := "SUCCESS"
	calculationMessage := "No voucher applied"

	if req.VoucherCode != nil && *req.VoucherCode != "" {
		voucher, err := s.voucherClient.CheckVoucherEligibility(
			ctx, req.UserID, *req.VoucherCode, req.OrderAmount, req.OrderTimestamp, req.Items)
		if err != nil {
			log.Errorf("Failed to check voucher eligibility: %v", err)
			calculationStatus = "FAILED"
			calculationMessage = fmt.Sprintf("Failed to validate voucher: %v", err)
		} else if !voucher.Eligible {
			calculationStatus = "FAILED"
			calculationMessage = voucher.Message
		} else {
			selectedVoucher = voucher
			calculationMessage = fmt.Sprintf("Voucher '%s' applied successfully", voucher.VoucherCode)
		}
	}

	var appliedVoucherID *uint64
	if selectedVoucher != nil && selectedVoucher.Eligible {
		appliedVoucherID = &selectedVoucher.VoucherID
	}

	order := model.NewOrder(
		req.UserID,
		req.OrderAmount,
		appliedVoucherID,
		calculationStatus,
		calculationMessage,
	)

	if err := s.repo.CreateWithItems(ctx, order, req.Items); err != nil {
		s.businessMetrics.RecordOrderCreation("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create order: %v", err))
	}

	if selectedVoucher != nil && selectedVoucher.Eligible && calculationStatus == "SUCCESS" {
		if err := s.voucherClient.IncrementVoucherUsage(ctx, selectedVoucher.VoucherID, req.UserID, order.ID, req.OrderAmount, selectedVoucher.DiscountAmount, req.OrderTimestamp); err != nil {
			log.Errorf("Failed to increment voucher usage for voucher %d: %v", selectedVoucher.VoucherID, err)
		}
	}

	s.businessMetrics.RecordOrderCreation("success", time.Since(start))
	log.Infof("Order created successfully with ID %d", order.ID)

	if s.eventPublisher != nil {
		go func() {
			calculation := &model.CreateOrderResponse{
				OrderAmount:        req.OrderAmount,
				AppliedVoucherCode: req.VoucherCode,
				DiscountAmount:     0,
				FinalAmount:        req.OrderAmount,
				Status:             calculationStatus,
				Message:            calculationMessage,
			}

			if selectedVoucher != nil && selectedVoucher.Eligible {
				calculation.AppliedVoucherCode = &selectedVoucher.VoucherCode
				calculation.DiscountAmount = selectedVoucher.DiscountAmount
				calculation.FinalAmount = req.OrderAmount - selectedVoucher.DiscountAmount
				if calculation.FinalAmount < 0 {
					calculation.FinalAmount = 0
				}
			}

			if err := s.eventPublisher.PublishOrderCreatedWithVoucherDetails(context.Background(), order, calculation); err != nil {
				s.logger.Errorf("Failed to publish order events: %v", err)
			}
		}()
	}

	return order, nil
}

func (s *orderService) GetOrder(ctx context.Context, orderID uint64) (*model.Order, error) {
	start := time.Now()

	order, err := s.repo.GetByID(ctx, orderID)
	if err != nil {
		s.businessMetrics.RecordOrderQuery("error", time.Since(start))
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("order with id %d not found", orderID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get order: %v", err))
	}

	s.businessMetrics.RecordOrderQuery("success", time.Since(start))
	return order, nil
}

func (s *orderService) ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error) {
	start := time.Now()

	result, err := s.repo.List(ctx, req)
	if err != nil {
		s.businessMetrics.RecordOrderQuery("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders: %v", err))
	}

	s.businessMetrics.RecordOrderQuery("success", time.Since(start))
	return result, nil
}

func (s *orderService) ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error) {
	start := time.Now()

	result, err := s.repo.ListByVoucher(ctx, req)
	if err != nil {
		s.businessMetrics.RecordOrderQuery("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders by voucher: %v", err))
	}

	s.businessMetrics.RecordOrderQuery("success", time.Since(start))
	return result, nil
}

func (s *orderService) GetUserOrderCount(ctx context.Context, userID uint64) (int64, error) {
	start := time.Now()
	count, err := s.repo.GetUserOrderCount(ctx, userID)
	if err != nil {
		s.businessMetrics.RecordOrderQuery("error", time.Since(start))
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user order count: %v", err))
	}

	s.businessMetrics.RecordOrderQuery("success", time.Since(start))
	return count, nil
}

func (s *orderService) GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int64, error) {
	start := time.Now()

	count, err := s.repo.GetUserVoucherUsageCount(ctx, userID, voucherID)
	if err != nil {
		s.businessMetrics.RecordOrderQuery("error", time.Since(start))
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user voucher usage count: %v", err))
	}

	s.businessMetrics.RecordOrderQuery("success", time.Since(start))
	return count, nil
}
