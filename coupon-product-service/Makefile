BINARY=product-server

.PHONY: build run test docker-build compose-up compose-down

build:
	go build -o $(BINARY) ./cmd/server

run: build
	./$(BINARY)

test:
	go test ./...

docker-build:
	docker build -t coupon-product-service .

compose-up:
	docker-compose up -d

compose-down:
	docker-compose down

insert-data:
	@echo "Inserting product data..."
	@chmod +x scripts/insert_product_data.sh
	@./scripts/insert_product_data.sh

