package model

import (
	"time"
)

type ProductStatus string

const (
	ProductStatusActive   ProductStatus = "ACTIVE"
	ProductStatusInactive ProductStatus = "INACTIVE"
)

type Category struct {
	ID          uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"column:name;type:varchar(255);not null;unique" json:"name"`
	Description string    `gorm:"column:description;type:text" json:"description"`
	CreatedAt   time.Time `gorm:"column:created_at;not null" json:"created_at"`
}

func (Category) TableName() string { return "categories" }

func NewCategory(name, description string) *Category {
	return &Category{
		Name:        name,
		Description: description,
	}
}

type Product struct {
	ID            uint64        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name          string        `gorm:"column:name;type:varchar(255);not null" json:"name"`
	Description   string        `gorm:"column:description;type:text" json:"description"`
	Price         float64       `gorm:"column:price;not null" json:"price"`
	CategoryID    uint64        `gorm:"column:category_id;not null" json:"category_id"`
	ImageURL      string        `gorm:"column:image_url;type:text" json:"image_url"`
	StockQuantity int64         `gorm:"column:stock_quantity;not null;default:0" json:"stock_quantity"`
	Status        ProductStatus `gorm:"column:status;type:varchar(50);not null;default:'ACTIVE'" json:"status"`
	Brand         string        `gorm:"column:brand;type:varchar(255)" json:"brand"`
	SKU           string        `gorm:"column:sku;type:varchar(255);unique" json:"sku"`
	CreatedAt     time.Time     `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt     time.Time     `gorm:"column:updated_at;not null" json:"updated_at"`

	Category *Category `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
}

func (Product) TableName() string { return "products" }

func NewProduct(name, description string, price float64, categoryID uint64, imageURL string, stockQuantity int64, status ProductStatus, brand string, sku string) *Product {
	return &Product{
		Name:          name,
		Description:   description,
		Price:         price,
		CategoryID:    categoryID,
		ImageURL:      imageURL,
		StockQuantity: stockQuantity,
		Status:        status,
		Brand:         brand,
		SKU:           sku,
	}
}

func GetAllModels() []any {
	return []any{
		&Category{},
		&Product{},
	}
}

type ListProductsRequest struct {
	Page       int           `json:"page" query:"page"`
	Limit      int           `json:"limit" query:"limit"`
	Search     string        `json:"search" query:"search"`
	CategoryID *uint64       `json:"category_id" query:"category_id"`
	Status     ProductStatus `json:"status" query:"status"`
	SortBy     string        `json:"sort_by" query:"sort_by"`
	SortOrder  string        `json:"sort_order" query:"sort_order"`
}

type UpdateProductRequest struct {
	Name          string        `json:"name" validate:"required,min=1,max=255"`
	Description   string        `json:"description"`
	Price         float64       `json:"price" validate:"required,gt=0"`
	CategoryID    uint64        `json:"category_id"`
	ImageURL      string        `json:"image_url"`
	StockQuantity int64         `json:"stock_quantity" validate:"gte=0"`
	Status        ProductStatus `json:"status"`
	Brand         string        `json:"brand"`
	SKU           string        `json:"sku"`
}
