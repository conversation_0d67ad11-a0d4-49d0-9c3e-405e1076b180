#!/bin/bash

set -e

load_env_file() {
    local env_files=(
        ".env"
        "coupon-product-service/.env"
        "../.env"
        "./.env"
    )

    for env_file in "${env_files[@]}"; do
        if [[ -f "$env_file" ]]; then
            echo "Loading environment variables from $env_file..."
            while IFS= read -r line; do
                [[ $line =~ ^[[:space:]]*# ]] && continue
                [[ -z "${line// }" ]] && continue
                export "$line"
            done < "$env_file"
            return 0
        fi
    done

    echo "Warning: No .env file found in common locations. Using default values or existing environment variables."
    echo "Searched locations: ${env_files[*]}"
    return 1
}

load_env_file

POSTGRES_CONTAINER="postgres-product"
DB_USER="${POSTGRES_USER}"
DB_PASSWORD="${POSTGRES_PASSWORD}"
DB_NAME="${POSTGRES_DB}"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_database_connection() {
    print_status "Checking PostgreSQL container connection..."

    if ! command -v docker &> /dev/null; then
        print_error "docker command not found. Please install Docker."
        exit 1
    fi

    if ! docker ps --format "table {{.Names}}" | grep -q "^${POSTGRES_CONTAINER}$"; then
        print_error "PostgreSQL container '${POSTGRES_CONTAINER}' is not running. Please check:"
        echo "  - Container name: $POSTGRES_CONTAINER"
        echo "  - Run 'docker ps' to see running containers"
        echo "  - Start the container if needed"
        exit 1
    fi

    if ! docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to database inside container. Please check:"
        echo "  - Container: $POSTGRES_CONTAINER"
        echo "  - User: $DB_USER"
        echo "  - Database: $DB_NAME"
        echo "  - Environment variables are set correctly"
        exit 1
    fi

    print_success "PostgreSQL container connection successful"
}

execute_sql_file() {
    local file_path="$1"
    local table_name="$2"

    if [[ ! -f "$file_path" ]]; then
        print_error "SQL file not found: $file_path"
        return 1
    fi

    print_status "Inserting data into $table_name table from $file_path..."

    local temp_error_file=$(mktemp)

    if docker cp "$file_path" "$POSTGRES_CONTAINER:/tmp/$(basename "$file_path")" && \
       docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/$(basename "$file_path")" 2>"$temp_error_file"; then
        print_success "Successfully inserted data into $table_name table"

        local count=$(docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
        print_status "Total rows in $table_name: $count"

        docker exec "$POSTGRES_CONTAINER" rm -f "/tmp/$(basename "$file_path")"
    else
        print_error "Failed to insert data into $table_name table"
        if [[ -s "$temp_error_file" ]]; then
            print_error "Error details:"
            cat "$temp_error_file"
        fi
        rm -f "$temp_error_file"
        return 1
    fi

    rm -f "$temp_error_file"
}

clear_existing_data() {
    print_warning "Clearing existing data from all tables..."

    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" << EOF
-- Clear data in reverse dependency order
DELETE FROM products;
DELETE FROM categories;

-- Reset sequences to start from 1
SELECT setval(pg_get_serial_sequence('categories', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('products', 'id'), 1, false);
EOF

    print_success "Existing data cleared and sequences reset"
}

main() {
    print_status "Starting product data insertion script..."
    print_status "Target PostgreSQL container: $POSTGRES_CONTAINER"
    print_status "Target database: $DB_NAME"

    print_status "Loaded configuration:"
    echo "  - Container: $POSTGRES_CONTAINER"
    echo "  - Database: $DB_NAME"
    echo "  - User: $DB_USER"
    echo "  - Password: ${DB_PASSWORD:0:3}***"
    echo ""
    
    check_database_connection

    clear_existing_data
    print_status "Starting fresh data insertion..."
    execute_sql_file "scripts/categories.sql" "categories"
    execute_sql_file "scripts/products.sql" "products"
         
    print_success "All data insertion completed successfully!"

    print_status "Configuration used:"
    echo "  - Container: $POSTGRES_CONTAINER"
    echo "  - Database: $DB_NAME"
    echo "  - User: $DB_USER"
    echo "  - Password: ${DB_PASSWORD:0:3}***"
    
    print_status "Final table statistics:"
    local tables=("categories" "products")

    for table in "${tables[@]}"; do
        local count=$(docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table;" | tr -d ' ' | tr -d '\n')
        printf "  %-35s: %s rows\n" "$table" "$count"
    done
}

main "$@"

