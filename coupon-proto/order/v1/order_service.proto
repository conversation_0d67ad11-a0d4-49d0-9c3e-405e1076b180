syntax = "proto3";

package order.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1";

service OrderService {
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  rpc GetOrder(GetOrderRequest) returns (GetOrderResponse);
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
  rpc ListOrdersByVoucher(ListOrdersByVoucherRequest) returns (ListOrdersByVoucherResponse);

  rpc GetUserOrderCount(GetUserOrderCountRequest) returns (GetUserOrderCountResponse);
  rpc GetUserVoucherUsageCount(GetUserVoucherUsageCountRequest) returns (GetUserVoucherUsageCountResponse);

  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message OrderItem {
  uint64 product_id = 1;
  uint64 quantity = 2;
  uint64 category_id = 3;
  double price = 4;
}

message Order {
  uint64 id = 1;
  uint64 user_id = 2;
  repeated OrderItem items = 3;
  double order_amount = 4;
  string calculation_status = 5;
  string calculation_message = 6;
  google.protobuf.Timestamp created_at = 7;
  optional uint64 applied_voucher_id = 8;
}

message CreateOrderRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 user_id = 2;
  repeated OrderItem items = 3;
  double order_amount = 4;
  google.protobuf.Timestamp order_timestamp = 5;
  optional string voucher_code = 6;
}

message CreateOrderResponse {
  common.v1.ResponseMetadata metadata = 1;
  Order order = 2;
  common.v1.ServiceError error = 3;
}

message GetOrderRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 order_id = 2;
}

message GetOrderResponse {
  common.v1.ResponseMetadata metadata = 1;
  Order order = 2;
  common.v1.ServiceError error = 3;
}

message ListOrdersRequest {
  common.v1.RequestMetadata metadata = 1;
  optional uint64 user_id = 2;
  common.v1.PaginationRequest pagination = 3;
  optional string search = 4;
}

message ListOrdersResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Order orders = 2;
  int32 total = 3;
  int32 page = 4;
  int32 limit = 5;
  common.v1.ServiceError error = 6;
}

message GetUserOrderCountRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 user_id = 2;
}

message GetUserOrderCountResponse {
  common.v1.ResponseMetadata metadata = 1;
  uint64 order_count = 2;
  common.v1.ServiceError error = 3;
}

message GetUserVoucherUsageCountRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 user_id = 2;
  uint64 voucher_id = 3;
}

message GetUserVoucherUsageCountResponse {
  common.v1.ResponseMetadata metadata = 1;
  uint64 usage_count = 2;
  common.v1.ServiceError error = 3;
}

message ListOrdersByVoucherRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 voucher_id = 2;
  common.v1.PaginationRequest pagination = 3;
}

message ListOrdersByVoucherResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Order orders = 2;
  int32 total = 3;
  int32 page = 4;
  int32 limit = 5;
  common.v1.ServiceError error = 6;
}
