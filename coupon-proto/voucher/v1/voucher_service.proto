syntax = "proto3";

package voucher.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1";

service VoucherService {
  rpc CreateVoucher(CreateVoucherRequest) returns (CreateVoucherResponse);
  rpc GetVoucher(GetVoucherRequest) returns (GetVoucherResponse);
  rpc GetVoucherByCode(GetVoucherByCodeRequest) returns (GetVoucherByCodeResponse);
  rpc UpdateVoucher(UpdateVoucherRequest) returns (UpdateVoucherResponse);
  rpc ListVouchers(ListVouchersRequest) returns (ListVouchersResponse);

  rpc CheckVoucherEligibility(CheckVoucherEligibilityRequest) returns (CheckVoucherEligibilityResponse);
  rpc ListAutoEligibleVouchers(ListAutoEligibleVouchersRequest) returns (ListAutoEligibleVouchersResponse);
  rpc IncrementVoucherUsage(IncrementVoucherUsageRequest) returns (IncrementVoucherUsageResponse);

  rpc GetDiscountTypes(GetDiscountTypesRequest) returns (GetDiscountTypesResponse);

  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

enum UsageMethod {
  USAGE_METHOD_UNSPECIFIED = 0;
  USAGE_METHOD_MANUAL = 1;
  USAGE_METHOD_AUTO = 2;
}

enum VoucherStatus {
  VOUCHER_STATUS_UNSPECIFIED = 0;
  VOUCHER_STATUS_ACTIVE = 1;
  VOUCHER_STATUS_INACTIVE = 2;
  VOUCHER_STATUS_EXPIRED = 3;
}

enum TimeRestrictionType {
  TIME_RESTRICTION_TYPE_UNSPECIFIED = 0;
  TIME_RESTRICTION_TYPE_DAYS_OF_WEEK = 1;
  TIME_RESTRICTION_TYPE_HOURS_OF_DAY = 2;
  TIME_RESTRICTION_TYPE_SPECIFIC_DATES = 3;
  TIME_RESTRICTION_TYPE_RECURRING_DATES = 4;
}

enum RecurrencePattern {
  RECURRENCE_PATTERN_UNSPECIFIED = 0;
  RECURRENCE_PATTERN_DAILY = 1;
  RECURRENCE_PATTERN_WEEKLY = 2;
  RECURRENCE_PATTERN_MONTHLY = 3;
  RECURRENCE_PATTERN_QUARTERLY = 4;
  RECURRENCE_PATTERN_YEARLY = 5;
}

message DiscountType {
  uint64 id = 1;
  string type_code = 2;
  string type_name = 3;
  string description = 4;
  bool is_active = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message CartItem {
  optional uint64 product_id = 1;
  optional uint64 category_id = 2;
  uint64 quantity = 3;
  double price = 4;
}

message VoucherProductRestriction {
  uint64 id = 1;
  google.protobuf.Timestamp created_at = 2;
  uint64 voucher_id = 3;
  optional uint64 product_id = 4;
  optional uint64 category_id = 5;
  optional string product_name = 6;
  optional string category_name = 7;
  bool is_included = 8;
}

message VoucherTimeRestriction {
  uint64 id = 1;
  google.protobuf.Timestamp created_at = 2;
  uint64 voucher_id = 3;
  TimeRestrictionType restriction_type = 4;
  repeated int32 allowed_days_of_weeks = 5;
  optional int32 allowed_hours_start = 6;
  optional int32 allowed_hours_end = 7;
  repeated google.protobuf.Timestamp specific_dates = 8;
  optional RecurrencePattern recurrence_pattern = 9;
  optional int32 recurrence_day_of_month = 10;
  optional int32 recurrence_month = 11;
  optional int32 recurrence_day_of_week = 12;
}

message VoucherUserEligibility {
  uint64 id = 1;
  google.protobuf.Timestamp created_at = 2;
  uint64 voucher_id = 3;
  optional uint64 user_id = 4;
  optional string user_type = 5;
  optional int32 min_account_age_days = 6;
  optional int32 max_account_age_days = 7;
  optional uint64 min_previous_orders = 8;
  optional uint64 max_previous_orders = 9;
}

message VoucherOrderUsage {
  uint64 order_id = 1;
  google.protobuf.Timestamp used_at = 2;
  double order_amount = 3;
  string status = 4;
}

message UserVoucherUsage {
  uint64 user_id = 1;
  int32 usage_count = 2;
  string full_name = 3;
  string email = 4;
  string type = 5;
  repeated VoucherOrderUsage orders = 6;
}

message Voucher {
  uint64 id = 1;
  string voucher_code = 2;
  string title = 3;
  string description = 4;
  uint64 discount_type_id = 5;
  double discount_value = 6;
  UsageMethod usage_method = 7;
  google.protobuf.Timestamp valid_from = 8;
  google.protobuf.Timestamp valid_until = 9;
  optional int32 max_usage_count = 10;
  optional int32 max_usage_per_user = 11;
  string user_eligibility_type = 12;
  int32 current_usage_count = 13;
  double min_order_amount = 14;
  optional double max_discount_amount = 15;
  uint64 created_by = 16;
  google.protobuf.Timestamp created_at = 17;
  google.protobuf.Timestamp updated_at = 18;

  optional DiscountType discount_type = 19;
  VoucherStatus status = 20;
  repeated VoucherProductRestriction product_restrictions = 21;
  repeated VoucherTimeRestriction time_restrictions = 22;
  repeated VoucherUserEligibility user_eligibility_rules = 23;
  repeated UserVoucherUsage user_usages = 24;
  double total_savings = 25;
  int32 unique_users = 26;
}

message CreateVoucherRequest {
  common.v1.RequestMetadata metadata = 1;
  string voucher_code = 2;
  string title = 3;
  string description = 4;
  uint64 discount_type_id = 5;
  double discount_value = 6;
  UsageMethod usage_method = 7;
  google.protobuf.Timestamp valid_from = 8;
  google.protobuf.Timestamp valid_until = 9;
  optional int32 max_usage_count = 10;
  optional int32 max_usage_per_user = 11;
  double min_order_amount = 12;
  optional double max_discount_amount = 13;
}

message CreateVoucherResponse {
  common.v1.ResponseMetadata metadata = 1;
  Voucher voucher = 2;
  common.v1.ServiceError error = 3;
}

message GetVoucherRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 voucher_id = 2;
}

message GetVoucherResponse {
  common.v1.ResponseMetadata metadata = 1;
  Voucher voucher = 2;
  common.v1.ServiceError error = 3;
}

message GetVoucherByCodeRequest {
  common.v1.RequestMetadata metadata = 1;
  string voucher_code = 2;
}

message GetVoucherByCodeResponse {
  common.v1.ResponseMetadata metadata = 1;
  Voucher voucher = 2;
  common.v1.ServiceError error = 3;
}

message UpdateVoucherRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 voucher_id = 2;
  string title = 3;
  string description = 4;
  uint64 discount_type_id = 5;
  double discount_value = 6;
  UsageMethod usage_method = 7;
  VoucherStatus status = 8;
  double min_order_amount = 9;
  optional double max_discount_amount = 10;
  optional int32 max_usage_count = 11;
  optional int32 max_usage_per_user = 12;
  google.protobuf.Timestamp valid_from = 13;
  google.protobuf.Timestamp valid_until = 14;
  repeated VoucherProductRestriction product_restrictions = 15;
  repeated VoucherTimeRestriction time_restrictions = 16;
  repeated VoucherUserEligibility user_eligibilities = 17;
}

message UpdateVoucherResponse {
  common.v1.ResponseMetadata metadata = 1;
  Voucher voucher = 2;
  common.v1.ServiceError error = 3;
}

message ListVouchersRequest {
  common.v1.RequestMetadata metadata = 1;
  int32 page = 2;
  int32 limit = 3;
  string search = 4;
  optional string discount_type_id = 5;
  optional UsageMethod usage_method = 6;
  string status = 7;
  string sort_by = 8;
  string sort_order = 9;
}

message ListVouchersResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Voucher vouchers = 2;
  int32 total = 3;
  int32 page = 4;
  int32 limit = 5;
  common.v1.ServiceError error = 6;
}

message GetDiscountTypesRequest {
  common.v1.RequestMetadata metadata = 1;
}

message GetDiscountTypesResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated DiscountType discount_types = 2;
  common.v1.ServiceError error = 3;
}

message CheckVoucherEligibilityRequest {
  common.v1.RequestMetadata metadata = 1;
  string voucher_code = 2;
  uint64 user_id = 3;
  double order_amount = 4;
  google.protobuf.Timestamp order_timestamp = 5;
  repeated CartItem cart_items = 6;
}

message CheckVoucherEligibilityResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool eligible = 2;
  string message = 3;
  uint64 voucher_id = 4;
  double discount_amount = 5;
  string discount_type_name = 6;
  string discount_type_code = 7;
  string voucher_code = 8;
  string title = 9;
  string description = 10;
  VoucherStatus status = 11;
  common.v1.ServiceError error = 12;
}

message EligibleVoucherInfo {
  bool eligible = 1;
  string message = 2;
  uint64 voucher_id = 3;
  double discount_amount = 4;
  string discount_type_name = 5;
  string discount_type_code = 6;
  string voucher_code = 7;
  string title = 8;
  string description = 9;
  VoucherStatus status = 10;
}

message ListAutoEligibleVouchersRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 user_id = 2;
  double order_amount = 3;
  google.protobuf.Timestamp order_timestamp = 4;
  repeated CartItem cart_items = 5;
}

message ListAutoEligibleVouchersResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated EligibleVoucherInfo vouchers = 2;
  common.v1.ServiceError error = 3;
}

message IncrementVoucherUsageRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 voucher_id = 2;
  uint64 user_id = 3;
  double order_amount = 4;
  google.protobuf.Timestamp order_timestamp = 5;
  uint64 order_id = 6;
  double discount_amount = 7;
}

message IncrementVoucherUsageResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool success = 2;
  string message = 3;
  int32 new_usage_count = 4;
  common.v1.ServiceError error = 5;
}
