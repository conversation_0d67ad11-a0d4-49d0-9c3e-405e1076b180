package metrics

import (
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

type Metrics struct {
	HTTPRequestsTotal   *prometheus.CounterVec
	HTTPRequestDuration *prometheus.HistogramVec
	HTTPRequestSize     *prometheus.HistogramVec
	HTTPResponseSize    *prometheus.HistogramVec

	GRPCRequestsTotal   *prometheus.CounterVec
	GRPCRequestDuration *prometheus.HistogramVec
	GRPCRequestSize     *prometheus.HistogramVec
	GRPCResponseSize    *prometheus.HistogramVec

	DatabaseQueries       *prometheus.CounterVec
	DatabaseQueryDuration *prometheus.HistogramVec
	DatabaseConnections   *prometheus.GaugeVec
	DatabaseErrors        *prometheus.CounterVec

	CacheHits     *prometheus.CounterVec
	CacheDuration *prometheus.HistogramVec
	CacheSize     *prometheus.GaugeVec

	ActiveConnections prometheus.Gauge
	MemoryUsage       *prometheus.GaugeVec
	CPUUsage          *prometheus.GaugeVec

	BusinessOperations *prometheus.CounterVec
	BusinessDuration   *prometheus.HistogramVec
	BusinessErrors     *prometheus.CounterVec

	AuthAttempts *prometheus.CounterVec
	AuthDuration *prometheus.HistogramVec

	KafkaMessages *prometheus.CounterVec
	KafkaLag      *prometheus.GaugeVec
	KafkaErrors   *prometheus.CounterVec
}

func New(serviceName string) *Metrics {
	return &Metrics{
		HTTPRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"service", "method", "endpoint", "status"},
		),
		HTTPRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
			},
			[]string{"service", "method", "endpoint", "status"},
		),
		HTTPRequestSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"service", "method", "endpoint"},
		),
		HTTPResponseSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"service", "method", "endpoint", "status"},
		),

		GRPCRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "grpc_server_handled_total",
				Help: "Total number of gRPC requests handled by the server",
			},
			[]string{"service", "grpc_method", "grpc_code"},
		),
		GRPCRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_handling_seconds",
				Help:    "Histogram of response latency (seconds) of gRPC that had been application-level handled by the server",
				Buckets: []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
			},
			[]string{"service", "grpc_method", "grpc_code"},
		),
		GRPCRequestSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_msg_received_total",
				Help:    "Total number of gRPC stream messages received by the server",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"service", "grpc_method"},
		),
		GRPCResponseSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_server_msg_sent_total",
				Help:    "Total number of gRPC stream messages sent by the server",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"service", "grpc_method"},
		),

		DatabaseQueries: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"service", "operation", "status"},
		),
		DatabaseQueryDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "database_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5},
			},
			[]string{"service", "operation", "status"},
		),
		DatabaseConnections: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "database_connections",
				Help: "Number of database connections",
			},
			[]string{"service", "state"},
		),
		DatabaseErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_errors_total",
				Help: "Total number of database errors",
			},
			[]string{"service", "operation", "error_type"},
		),

		CacheHits: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "cache_operations_total",
				Help: "Total number of cache operations",
			},
			[]string{"service", "operation", "status"},
		),
		CacheDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "cache_operation_duration_seconds",
				Help:    "Cache operation duration in seconds",
				Buckets: []float64{.0001, .0005, .001, .005, .01, .025, .05, .1, .25, .5},
			},
			[]string{"service", "operation"},
		),
		CacheSize: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "cache_size_bytes",
				Help: "Cache size in bytes",
			},
			[]string{"service", "cache_type"},
		),

		ActiveConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "active_connections",
				Help: "Number of active connections",
			},
		),
		MemoryUsage: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
			[]string{"service", "type"},
		),
		CPUUsage: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "CPU usage percentage",
			},
			[]string{"service"},
		),

		BusinessOperations: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "business_operations_total",
				Help: "Total number of business operations",
			},
			[]string{"service", "operation", "status"},
		),
		BusinessDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "business_operation_duration_seconds",
				Help:    "Business operation duration in seconds",
				Buckets: []float64{.01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10, 30},
			},
			[]string{"service", "operation"},
		),
		BusinessErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "business_errors_total",
				Help: "Total number of business errors",
			},
			[]string{"service", "operation", "error_type"},
		),

		AuthAttempts: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "auth_attempts_total",
				Help: "Total number of authentication attempts",
			},
			[]string{"service", "method", "status"},
		),
		AuthDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "auth_duration_seconds",
				Help:    "Authentication duration in seconds",
				Buckets: []float64{.001, .005, .01, .025, .05, .1, .25, .5, 1},
			},
			[]string{"service", "method"},
		),

		KafkaMessages: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "kafka_messages_total",
				Help: "Total number of Kafka messages",
			},
			[]string{"service", "topic", "operation", "status"},
		),
		KafkaLag: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "kafka_consumer_lag",
				Help: "Kafka consumer lag",
			},
			[]string{"service", "topic", "partition"},
		),
		KafkaErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "kafka_errors_total",
				Help: "Total number of Kafka errors",
			},
			[]string{"service", "topic", "operation", "error_type"},
		),
	}
}

func (m *Metrics) RecordHTTPRequest(service, method, endpoint string, status int, duration time.Duration) {
	statusStr := strconv.Itoa(status)
	m.HTTPRequestsTotal.WithLabelValues(service, method, endpoint, statusStr).Inc()
	m.HTTPRequestDuration.WithLabelValues(service, method, endpoint, statusStr).Observe(duration.Seconds())
}

func (m *Metrics) RecordHTTPRequestSize(service, method, endpoint string, size float64) {
	m.HTTPRequestSize.WithLabelValues(service, method, endpoint).Observe(size)
}

func (m *Metrics) RecordHTTPResponseSize(service, method, endpoint string, status int, size float64) {
	statusStr := strconv.Itoa(status)
	m.HTTPResponseSize.WithLabelValues(service, method, endpoint, statusStr).Observe(size)
}

func (m *Metrics) RecordGRPCRequest(service, method, status string, duration time.Duration) {
	grpcCode := "OK"
	if status == "error" {
		grpcCode = "Unknown"
	}

	m.GRPCRequestsTotal.WithLabelValues(service, method, grpcCode).Inc()
	m.GRPCRequestDuration.WithLabelValues(service, method, grpcCode).Observe(duration.Seconds())
}

func (m *Metrics) RecordGRPCRequestSize(service, method string, size float64) {
	m.GRPCRequestSize.WithLabelValues(service, method).Observe(size)
}

func (m *Metrics) RecordGRPCResponseSize(service, method, status string, size float64) {
	m.GRPCResponseSize.WithLabelValues(service, method).Observe(size)
}

func (m *Metrics) RecordDatabaseQuery(service, operation, status string, duration time.Duration) {
	m.DatabaseQueries.WithLabelValues(service, operation, status).Inc()
	m.DatabaseQueryDuration.WithLabelValues(service, operation, status).Observe(duration.Seconds())
}

func (m *Metrics) SetDatabaseConnections(service, state string, count float64) {
	m.DatabaseConnections.WithLabelValues(service, state).Set(count)
}

func (m *Metrics) RecordDatabaseError(service, operation, errorType string) {
	m.DatabaseErrors.WithLabelValues(service, operation, errorType).Inc()
}

func (m *Metrics) RecordCacheOperation(service, operation, status string) {
	m.CacheHits.WithLabelValues(service, operation, status).Inc()
}

func (m *Metrics) RecordCacheDuration(service, operation string, duration time.Duration) {
	m.CacheDuration.WithLabelValues(service, operation).Observe(duration.Seconds())
}

func (m *Metrics) SetCacheSize(service, cacheType string, size float64) {
	m.CacheSize.WithLabelValues(service, cacheType).Set(size)
}

func (m *Metrics) SetActiveConnections(count int) {
	m.ActiveConnections.Set(float64(count))
}

func (m *Metrics) SetMemoryUsage(service, memType string, bytes float64) {
	m.MemoryUsage.WithLabelValues(service, memType).Set(bytes)
}

func (m *Metrics) SetCPUUsage(service string, percent float64) {
	m.CPUUsage.WithLabelValues(service).Set(percent)
}

func (m *Metrics) RecordBusinessOperation(service, operation, status string, duration time.Duration) {
	m.BusinessOperations.WithLabelValues(service, operation, status).Inc()
	m.BusinessDuration.WithLabelValues(service, operation).Observe(duration.Seconds())
}

func (m *Metrics) RecordBusinessError(service, operation, errorType string) {
	m.BusinessErrors.WithLabelValues(service, operation, errorType).Inc()
}

func (m *Metrics) RecordAuthAttempt(service, method, status string, duration time.Duration) {
	m.AuthAttempts.WithLabelValues(service, method, status).Inc()
	m.AuthDuration.WithLabelValues(service, method).Observe(duration.Seconds())
}

func (m *Metrics) RecordKafkaMessage(service, topic, operation, status string) {
	m.KafkaMessages.WithLabelValues(service, topic, operation, status).Inc()
}

func (m *Metrics) SetKafkaLag(service, topic, partition string, lag float64) {
	m.KafkaLag.WithLabelValues(service, topic, partition).Set(lag)
}

func (m *Metrics) RecordKafkaError(service, topic, operation, errorType string) {
	m.KafkaErrors.WithLabelValues(service, topic, operation, errorType).Inc()
}

func (m *Metrics) Handler() http.Handler {
	return promhttp.Handler()
}
