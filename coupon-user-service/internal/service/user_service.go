package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
)

type UserService interface {
	RegisterUser(ctx context.Context, name, email, password string) (*model.User, error)
	GetUser(ctx context.Context, userID uint64) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	Login(ctx context.Context, email, password string) (*model.User, error)
}

type userService struct {
	repo            repository.UserRepository
	eventPublisher  *UserEventPublisher
	logger          *logging.Logger
	businessMetrics *metrics.BusinessMetrics
}

func NewUserService(repo repository.UserRepository, eventPublisher *UserEventPublisher, logger *logging.Logger, businessMetrics *metrics.BusinessMetrics) UserService {
	return &userService{
		repo:            repo,
		eventPublisher:  eventPublisher,
		logger:          logger,
		businessMetrics: businessMetrics,
	}
}

func (s *userService) RegisterUser(ctx context.Context, name, email, password string) (*model.User, error) {
	start := time.Now()

	log := s.logger.WithContext(ctx)
	log.Infof("Attempting to register user with email: %s", email)

	_, err := s.repo.GetByEmail(ctx, email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		s.businessMetrics.RecordUserRegistration("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("db error checking for existing user: %v", err))
	}
	if err == nil {
		s.businessMetrics.RecordUserRegistration("error", time.Since(start))
		return nil, app_errors.NewConflictError(fmt.Sprintf("user with email %s already exists", email))
	}

	user, err := model.NewUser(name, email, password)
	if err != nil {
		s.businessMetrics.RecordUserRegistration("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to prepare user: %v", err))
	}
	if err := s.repo.Create(ctx, user); err != nil {
		s.businessMetrics.RecordUserRegistration("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create user profile: %v", err))
	}
	log.Infof("User profile created for user_id: %d", user.ID)

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishUserCreated(context.Background(), user); err != nil {
				s.logger.Errorf("Failed to publish user created event: %v", err)
			}
		}()
	}

	s.businessMetrics.RecordUserRegistration("success", time.Since(start))
	return user, nil
}

func (s *userService) GetUser(ctx context.Context, userID uint64) (*model.User, error) {
	user, err := s.repo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with id %d not found", userID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}
	return user, nil
}

func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.repo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with email %s not found", email))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user by email: %v", err))
	}
	return user, nil
}

func (s *userService) Login(ctx context.Context, email, password string) (*model.User, error) {
	start := time.Now()

	user, err := s.repo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.businessMetrics.RecordUserLogin("error", time.Since(start))
			return nil, app_errors.NewUnauthorizedError("invalid credentials")
		}
		s.businessMetrics.RecordUserLogin("error", time.Since(start))
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}

	if !user.CheckPassword(password) {
		s.businessMetrics.RecordUserLogin("error", time.Since(start))
		return nil, app_errors.NewUnauthorizedError("invalid credentials")
	}

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishUserLogin(context.Background(), user, "", ""); err != nil {
				s.logger.Errorf("Failed to publish user login event: %v", err)
			}
		}()
	}

	s.businessMetrics.RecordUserLogin("success", time.Since(start))
	return user, nil
}
