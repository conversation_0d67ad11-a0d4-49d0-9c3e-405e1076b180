BINARY=voucher-server

.PHONY: build run test docker-build compose-up compose-down

build:
	go build -o $(BINARY) ./cmd/server

run: build
	./$(BINARY)

test:
	go test ./...

docker-build:
	docker build -t voucher-service .

compose-up:
	docker-compose up -d

compose-down:
	docker-compose down

insert-data:
	@echo "Inserting voucher data..."
	@chmod +x scripts/insert_voucher_data.sh
	@./scripts/insert_voucher_data.sh

