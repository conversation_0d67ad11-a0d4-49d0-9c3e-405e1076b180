package clients

import (
	"context"
	"fmt"
	"time"

	proto_order_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type OrderClient struct {
	client proto_order_v1.OrderServiceClient
	conn   *shared_grpc.Client
}

func NewOrderClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, serviceName, clientID, clientKey string) (*OrderClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, serviceName, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create authenticated gRPC client for order service: %w", err)
	}

	return &OrderClient{
		client: proto_order_v1.NewOrderServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *OrderClient) Close() {
	c.conn.Close()
}

func (c *OrderClient) GetUserOrderCount(ctx context.Context, userID uint64) (uint64, error) {
	req := &proto_order_v1.GetUserOrderCountRequest{
		UserId: userID,
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUserOrderCount(ctx, req)
	if err != nil {
		return 0, err
	}

	if resp.Error != nil {
		return 0, fmt.Errorf("order service error: %s", resp.Error.Message)
	}

	return resp.OrderCount, nil
}

func (c *OrderClient) GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (uint64, error) {
	req := &proto_order_v1.GetUserVoucherUsageCountRequest{
		UserId:    userID,
		VoucherId: voucherID,
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUserVoucherUsageCount(ctx, req)
	if err != nil {
		return 0, err
	}

	if resp.Error != nil {
		return 0, fmt.Errorf("order service error: %s", resp.Error.Message)
	}

	return resp.UsageCount, nil
}
